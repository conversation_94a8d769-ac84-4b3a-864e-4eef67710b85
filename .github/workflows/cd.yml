run-name: Build push and deploy (CD)
on:
  push:
    branches:
      - main
      - ci-debug
    tags:
      - '*'

jobs:
  build-and-push-to-gcr:
    runs-on: ubuntu-latest
    concurrency:
      group: ${{ github.ref_type == 'branch' && github.ref }}
      cancel-in-progress: true
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true
      - uses: 'google-github-actions/auth@v2'
        with:
           credentials_json: '${{ secrets.GCLOUD_SERVICE_ACCOUNT_SECRET_JSON }}'
      - name: 'Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
      - name: "Docker auth"
        run: |-
          gcloud auth configure-docker us-docker.pkg.dev --quiet
      - name: Set controller release version
        run: echo "RELEASE_VERSION=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.12.0
          cache: npm

      - name: npm install
        run: npm ci
      - name: build application
        run: npm run build
      - name: Set package version
        run: npm version --no-git-tag-version ${{ env.RELEASE_VERSION }}
        if: github.ref_type == 'tag'
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            us-docker.pkg.dev/research-df067/deepresearch/node-deep-research
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push
        id: container
        uses: docker/build-push-action@v6
        with:
          file: jina-ai/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
      - name: Deploy with Tag
        run: |
          gcloud run deploy node-deep-research --image us-docker.pkg.dev/research-df067/deepresearch/node-deep-research@${{steps.container.outputs.imageid}} --tag ${{ env.RELEASE_VERSION }} --region us-central1 --async
      