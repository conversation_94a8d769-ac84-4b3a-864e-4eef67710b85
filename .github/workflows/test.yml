name: Test

on:
  pull_request:
    branches: [ main ]

jobs:
  test:
    if: "!startsWith(github.event.head_commit.message, 'chore')"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint

      - name: Run tests
        env:
          BRAVE_API_KEY: ${{ secrets.BRAVE_API_KEY }}
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          JINA_API_KEY: ${{ secrets.JINA_API_KEY }}
          GOOGLE_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: npm test

      - name: Set up Docker
        uses: docker/setup-buildx-action@v3

      - name: Run Docker tests
        env:
          BRAVE_API_KEY: mock_key
          GEMINI_API_KEY: mock_key
          JINA_API_KEY: mock_key
          GOOGLE_API_KEY: mock_key
          OPENAI_API_KEY: mock_key
        run: npm run test:docker
